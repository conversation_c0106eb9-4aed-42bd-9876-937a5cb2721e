'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'

const Footer: React.FC = () => {
  const quickLinks = [
    { name: 'Home', href: '/adukrom' },
    { name: 'Events', href: '/events' },
    { name: 'News', href: '/news' },
    { name: 'Partners', href: '/partners' },
    { name: 'Gallery', href: '/gallery' },
    { name: 'Streaming', href: '/streaming' },
    { name: 'Tickets', href: '/tickets' }
  ]

  const socialLinks = [
    { icon: 'fab fa-facebook-f', href: '#', color: 'hover:text-blue-500' },
    { icon: 'fab fa-twitter', href: '#', color: 'hover:text-blue-400' },
    { icon: 'fab fa-instagram', href: '#', color: 'hover:text-pink-500' },
    { icon: 'fab fa-youtube', href: '#', color: 'hover:text-red-500' }
  ]

  return (
    <footer className="relative bg-royalBlue text-white overflow-hidden">
      {/* Golden gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/15 via-royalGold/5 to-royalGold/10"></div>
      <div className="absolute inset-0 bg-gradient-to-bl from-transparent via-royalGold/3 to-royalGold/8"></div>
      <div className="container mx-auto px-4 py-16 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and Description */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="md:col-span-2"
          >
            <div className="flex items-center mb-4">
              <div className="mr-3 text-royalGold text-3xl">👑</div>
              <div>
                <h3 className="text-xl font-serif font-bold">Adukrom Kingdom</h3>
                <p className="text-royalGold text-sm">The Crown of Africa: Rise of a New Era</p>
              </div>
            </div>
            <p className="text-white/80 leading-relaxed mb-6 max-w-md">
              The official royal website of His Royal Majesty. Discover the heritage, initiatives, and vision that shape our kingdom's future.
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social, index) => (
                <motion.a
                  key={index}
                  href={social.href}
                  className={`w-10 h-10 rounded-full bg-royalGold/20 flex items-center justify-center text-royalGold transition-colors duration-300 ${social.color}`}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <i className={social.icon}></i>
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* Quick Links */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
          >
            <h4 className="text-lg font-bold mb-4 text-royalGold">Quick Links</h4>
            <ul className="space-y-2">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <Link 
                    href={link.href}
                    className="text-white/80 hover:text-royalGold transition-colors duration-300 text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Contact Info */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <h4 className="text-lg font-bold mb-4 text-royalGold">Contact Us</h4>
            <div className="space-y-3 text-sm">
              <div className="flex items-start">
                <i className="fas fa-map-marker-alt text-royalGold mr-3 mt-1"></i>
                <div>
                  <p className="text-white/80">Royal Palace</p>
                  <p className="text-white/80">Accra, Ghana</p>
                </div>
              </div>
              <div className="flex items-center">
                <i className="fas fa-phone text-royalGold mr-3"></i>
                <p className="text-white/80">+233 (0) 123 456 789</p>
              </div>
              <div className="flex items-center">
                <i className="fas fa-envelope text-royalGold mr-3"></i>
                <p className="text-white/80"><EMAIL></p>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Copyright */}
        <motion.div 
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="border-t border-white/10 mt-8 pt-8 text-center"
        >
          <p className="text-white/60 text-sm">
            © 2025 Adukrom Kingdom. All rights reserved.
          </p>
          <div className="flex justify-center space-x-6 mt-4">
            <a href="#" className="text-white/60 hover:text-royalGold transition-colors duration-300 text-sm">Privacy Policy</a>
            <a href="#" className="text-white/60 hover:text-royalGold transition-colors duration-300 text-sm">Terms of Service</a>
            <a href="#" className="text-white/60 hover:text-royalGold transition-colors duration-300 text-sm">Cookie Policy</a>
          </div>
        </motion.div>
      </div>
    </footer>
  )
}

export default Footer
