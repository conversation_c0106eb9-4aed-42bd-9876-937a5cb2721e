'use client'

import { motion } from 'framer-motion'

const Events: React.FC = () => {
  const upcomingEvents = [
    {
      title: 'Royal Coronation Ceremony',
      date: 'August 29, 2025',
      time: '3:30 AM EST',
      location: 'Adukrom Palace, Ghana',
      description: 'The historic coronation ceremony of His Royal Majesty, marking the beginning of a new era for the Kingdom.',
      category: 'Royal Ceremony',
      status: 'Upcoming',
      image: '/api/placeholder/400/250',
      ticketsAvailable: true
    },
    {
      title: 'Royal Gala Dinner',
      date: 'August 30, 2025',
      time: '8:30 AM EST',
      location: 'Grand Ballroom, Accra',
      description: 'An elegant evening of celebration featuring traditional performances, fine dining, and networking with distinguished guests.',
      category: 'Royal Event',
      status: 'Upcoming',
      image: '/api/placeholder/400/250',
      ticketsAvailable: true
    },
    {
      title: 'Global Economic Forum',
      date: 'August 31, 2025',
      time: '10:00 PM EST',
      location: 'Adukrom Convention Center',
      description: 'A forward-looking conference bringing together investors, entrepreneurs, and leaders to explore economic opportunities and partnerships with the Kingdom.',
      category: 'Business',
      status: 'Upcoming',
      image: '/api/placeholder/400/250',
      ticketsAvailable: false
    },
    {
      title: 'Youth Leadership Forum',
      date: 'September 5, 2025',
      time: '11:00 AM EST',
      location: 'Royal Academy',
      description: 'Empowering the next generation of leaders through mentorship and educational programs.',
      category: 'Education',
      status: 'Upcoming',
      image: '/api/placeholder/400/250',
      ticketsAvailable: true
    }
  ]

  const pastEvents = [
    {
      title: 'Royal Charity Gala',
      date: 'December 10, 2024',
      location: 'Royal Ballroom',
      description: 'A successful charity gala raising funds for education initiatives across Ghana.',
      category: 'Charity',
      status: 'Completed',
      image: '/api/placeholder/400/250'
    },
    {
      title: 'Traditional Arts Exhibition',
      date: 'November 15, 2024',
      location: 'Royal Museum',
      description: 'Showcasing the finest traditional arts and crafts from across the Kingdom.',
      category: 'Cultural',
      status: 'Completed',
      image: '/api/placeholder/400/250'
    }
  ]

  return (
    <section className="py-20 relative overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-royalBlue via-royalBlue/95 to-royalBlue/90"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/20 via-royalGold/5 to-royalGold/15"></div>
      <div className="absolute inset-0 bg-gradient-to-bl from-transparent via-royalGold/3 to-royalGold/8"></div>
      
      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-serif font-bold text-white mb-4">
            Royal Events
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-lg text-white/90 max-w-3xl mx-auto leading-relaxed">
            Join us for exclusive royal events, cultural celebrations, and meaningful gatherings that shape our Kingdom's future.
          </p>
        </motion.div>

        {/* Upcoming Events */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <h3 className="text-2xl font-serif font-bold text-white mb-8">Upcoming Events</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {upcomingEvents.map((event, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -10 }}
                className="bg-white/10 backdrop-blur-lg rounded-2xl overflow-hidden border border-white/20 shadow-xl"
              >
                <div className="h-48 bg-gradient-to-br from-royalGold/20 to-royalBlue/20 flex items-center justify-center">
                  <i className="fas fa-calendar-alt text-6xl text-royalGold"></i>
                </div>
                
                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className="px-3 py-1 bg-royalGold/20 text-royalGold text-xs font-semibold rounded-full">
                      {event.category}
                    </span>
                    {event.ticketsAvailable && (
                      <span className="px-3 py-1 bg-green-500/20 text-green-400 text-xs font-semibold rounded-full">
                        Tickets Available
                      </span>
                    )}
                  </div>
                  
                  <h4 className="text-xl font-bold text-white mb-2">{event.title}</h4>
                  <p className="text-white/80 text-sm mb-4 leading-relaxed">{event.description}</p>
                  
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center text-white/70 text-sm">
                      <i className="fas fa-calendar mr-2 text-royalGold"></i>
                      {event.date} at {event.time}
                    </div>
                    <div className="flex items-center text-white/70 text-sm">
                      <i className="fas fa-map-marker-alt mr-2 text-royalGold"></i>
                      {event.location}
                    </div>
                  </div>
                  
                  <div className="flex gap-3">
                    <motion.button
                      className="flex-1 px-4 py-2 bg-white/10 border border-white/20 text-white text-sm font-semibold rounded-lg hover:bg-white/20 transition-all duration-300"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      Learn More
                    </motion.button>
                    {event.ticketsAvailable && (
                      <motion.button
                        className="px-4 py-2 relative overflow-hidden text-royalBlue font-bold text-sm rounded-lg shadow-lg border border-yellow-300 group"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        style={{
                          background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%)',
                          boxShadow: '0 4px 16px rgba(255, 215, 0, 0.3)'
                        }}
                      >
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
                        <span className="relative z-10">Get Tickets</span>
                      </motion.button>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Past Events */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h3 className="text-2xl font-serif font-bold text-white mb-8">Past Events</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {pastEvents.map((event, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white/5 backdrop-blur-lg rounded-2xl overflow-hidden border border-white/10 shadow-lg opacity-80"
              >
                <div className="h-32 bg-gradient-to-br from-gray-500/20 to-gray-700/20 flex items-center justify-center">
                  <i className="fas fa-history text-4xl text-gray-400"></i>
                </div>
                
                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className="px-3 py-1 bg-gray-500/20 text-gray-400 text-xs font-semibold rounded-full">
                      {event.category}
                    </span>
                    <span className="px-3 py-1 bg-gray-600/20 text-gray-400 text-xs font-semibold rounded-full">
                      Completed
                    </span>
                  </div>
                  
                  <h4 className="text-lg font-bold text-white/80 mb-2">{event.title}</h4>
                  <p className="text-white/60 text-sm mb-4 leading-relaxed">{event.description}</p>
                  
                  <div className="flex items-center text-white/50 text-sm">
                    <i className="fas fa-calendar mr-2"></i>
                    {event.date} • {event.location}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Events
