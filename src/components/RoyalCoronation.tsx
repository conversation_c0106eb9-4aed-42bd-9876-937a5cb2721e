'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'

const RoyalCoronation: React.FC = () => {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  })

  useEffect(() => {
    const targetDate = new Date('2025-08-29T03:30:00-05:00').getTime() // EST timezone

    const timer = setInterval(() => {
      const now = new Date().getTime()
      const difference = targetDate - now

      const days = Math.floor(difference / (1000 * 60 * 60 * 24))
      const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((difference % (1000 * 60)) / 1000)

      setTimeLeft({ days, hours, minutes, seconds })

      if (difference < 0) {
        clearInterval(timer)
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 })
      }
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const events = [
    {
      title: 'Royal Coronation Ceremony',
      date: 'August 29, 2025',
      time: '3:30 AM EST',
      location: 'Adukrom Palace, Ghana',
      description: 'The official coronation ceremony where His Royal Majesty will be crowned according to ancient royal traditions, with dignitaries from across Ghana and international representatives in attendance.',
      gradient: 'from-royalGold to-yellow-500',
      icon: 'fas fa-crown'
    },
    {
      title: 'Royal Gala Dinner',
      date: 'August 30, 2025',
      time: '8:30 AM EST',
      location: 'Grand Ballroom, Accra',
      description: 'An elegant evening of celebration featuring traditional performances, fine dining, and networking with distinguished guests from business, politics, and cultural spheres.',
      gradient: 'from-purple-600 to-pink-600',
      icon: 'fas fa-utensils'
    },
    {
      title: 'Global Economic Forum',
      date: 'August 31, 2025',
      time: '10:00 PM EST',
      location: 'Adukrom Convention Center',
      description: 'A forward-looking conference bringing together investors, entrepreneurs, and leaders to explore economic opportunities and partnerships with the Kingdom.',
      gradient: 'from-royalBlue to-blue-600',
      icon: 'fas fa-handshake'
    }
  ]

  return (
    <section id="coronation" className="py-20 relative overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-royalBlue via-royalBlue/95 to-royalBlue/90"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/20 via-royalGold/5 to-royalGold/15"></div>
      <div className="absolute inset-0 bg-gradient-to-bl from-transparent via-royalGold/3 to-royalGold/8"></div>
      
      {/* Animated background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="royal-pattern w-full h-full"></div>
      </div>

      {/* Floating elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          animate={{ 
            x: [0, 100, 0],
            y: [0, -50, 0],
            rotate: [0, 180, 360]
          }}
          transition={{ 
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-10 right-10 w-64 h-64 bg-gradient-to-br from-royalGold/20 to-transparent rounded-full blur-3xl"
        />
        <motion.div
          animate={{ 
            x: [0, -80, 0],
            y: [0, 60, 0],
            rotate: [0, -90, -180]
          }}
          transition={{ 
            duration: 15,
            repeat: Infinity,
            ease: "linear",
            delay: 5
          }}
          className="absolute bottom-10 left-10 w-80 h-80 bg-gradient-to-tl from-white/10 to-transparent rounded-full blur-2xl"
        />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-serif font-bold text-white mb-4">
            Royal Coronation 2025
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-lg text-white/90 max-w-3xl mx-auto leading-relaxed">
            Join us for the historic coronation ceremony of His Royal Majesty, a once-in-a-generation celebration of Ghana's royal heritage.
          </p>
        </motion.div>



        {/* Events Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
          {events.map((event, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              viewport={{ once: true }}
              whileHover={{ y: -10, scale: 1.02 }}
              className="group relative"
            >
              <div className="bg-white/10 backdrop-blur-lg rounded-3xl overflow-hidden border border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-300">
                {/* Event Header */}
                <div className={`bg-gradient-to-r ${event.gradient} p-6 relative overflow-hidden`}>
                  <div className="absolute inset-0 bg-black/10"></div>
                  <div className="relative z-10 flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-bold text-white mb-1">{event.title}</h3>
                      <p className="text-white/90 text-sm">{event.date}</p>
                    </div>
                    <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                      <i className={`${event.icon} text-white text-xl`}></i>
                    </div>
                  </div>
                </div>

                {/* Event Content */}
                <div className="p-6">
                  <div className="mb-4">
                    <div className="flex items-center text-white/80 text-sm mb-2">
                      <i className="fas fa-clock mr-2 text-royalGold"></i>
                      {event.time}
                    </div>
                    <div className="flex items-center text-white/80 text-sm mb-4">
                      <i className="fas fa-map-marker-alt mr-2 text-royalGold"></i>
                      {event.location}
                    </div>
                  </div>
                  
                  <p className="text-white/90 text-sm leading-relaxed mb-6">
                    {event.description}
                  </p>

                  <motion.a
                    href="#rsvp"
                    className="block w-full text-center py-3 px-6 bg-gradient-to-r from-royalGold to-yellow-500 text-royalBlue font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    RSVP for This Event
                  </motion.a>
                </div>

                {/* Hover effect overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-royalGold/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-3xl"></div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Additional Information */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20 shadow-2xl max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-4">Need Assistance?</h3>
            <p className="text-white/90 mb-6 leading-relaxed">
              For accommodation and travel information, please contact our Royal Hospitality Team. 
              We're here to ensure your experience is memorable and comfortable.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.a
                href="#contact"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-royalGold to-yellow-500 text-royalBlue font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <i className="fas fa-phone mr-2"></i>
                Contact Hospitality Team
              </motion.a>
              <motion.a
                href="#rsvp"
                className="inline-flex items-center px-6 py-3 bg-white/20 backdrop-blur-md border border-white/30 text-white font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <i className="fas fa-calendar-check mr-2"></i>
                RSVP Now
              </motion.a>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default RoyalCoronation
