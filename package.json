{"name": "royal-ghana-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.0.18", "framer-motion": "^10.16.16", "next": "^15.0.0", "react": "^18", "react-dom": "^18"}, "devDependencies": {"@types/node": "^24.0.0", "@types/react": "^19.1.7", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.0.1", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5.8.3"}}