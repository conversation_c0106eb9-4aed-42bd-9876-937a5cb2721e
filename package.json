{"name": "royal-ghana-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.0.18", "framer-motion": "^11.0.0", "next": "15.3.3", "react": "^18.3.0", "react-dom": "^18.3.0"}, "devDependencies": {"@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}